# 触发器对象池使用示例

本文档提供触发器对象池的实际使用示例，展示如何在游戏中配置和使用触发器对象池。

## 初始化对象池

以下是在游戏启动时初始化触发器对象池的示例：

```csharp
using UnityEngine;
using TriggerSystem;

public class GameInitializer : MonoBehaviour
{
    [SerializeField] private bool _verboseLogging = false;
    
    // 对象池配置
    [SerializeField] private int _maxPoolSize = 150;
    [SerializeField] private int _preAllocateCount = 15;
    [SerializeField] private bool _autoExpand = true;
    [SerializeField] private int _expandCount = 8;
    
    private void Awake()
    {
        // 初始化触发器系统
        TriggerAPI.InitializeTriggerSystem();
        
        // 配置触发器对象池
        ConfigureTriggerPool();
        
        // 配置异常处理
        TriggerAPI.SetExceptionHandling(true);
        TriggerAPI.SetVerboseLogging(_verboseLogging);
        
        Debug.Log("触发器系统初始化完成");
    }
    
    private void ConfigureTriggerPool()
    {
        var pool = TriggerPool.Instance;
        
        // 设置最大池大小
        pool.SetMaxPoolSize(_maxPoolSize);
        
        // 设置预分配数量
        pool.SetPreAllocateCount(_preAllocateCount);
        
        // 配置自动扩容
        pool.SetAutoExpand(_autoExpand);
        pool.SetExpandCount(_expandCount);
        
        // 配置日志详细程度
        pool.SetVerboseLogging(_verboseLogging);
        
        Debug.Log("触发器对象池配置完成");
    }
    
    // 可选：在游戏结束时输出统计信息
    private void OnApplicationQuit()
    {
        if (TriggerPool.Instance != null)
        {
            TriggerPool.Instance.LogPoolStats();
        }
    }
}
```

## 监控和调整对象池

在游戏运行时监控和调整对象池性能的示例：

```csharp
using UnityEngine;
using TriggerSystem;

public class TriggerPoolMonitor : MonoBehaviour
{
    [SerializeField] private bool _showStats = false;
    [SerializeField] private float _monitorInterval = 30f; // 每30秒监控一次
    
    // 自适应调整参数
    [SerializeField] private bool _enableAutoAdjust = true;
    [SerializeField] private float _expandThreshold = 0.9f; // 当池使用率超过90%时扩容
    [SerializeField] private float _trimThreshold = 0.3f;   // 当池使用率低于30%时裁剪
    
    private float _timer = 0f;
    
    private void Update()
    {
        if (!_showStats) return;
        
        _timer += Time.deltaTime;
        
        if (_timer >= _monitorInterval)
        {
            _timer = 0f;
            
            // 显示统计信息
            ShowPoolStats();
            
            // 自适应调整
            if (_enableAutoAdjust)
            {
                AdjustPoolSize();
            }
        }
    }
    
    private void ShowPoolStats()
    {
        string stats = TriggerPool.Instance.GetPoolStats();
        Debug.Log($"触发器对象池状态:\n{stats}");
    }
    
    private void AdjustPoolSize()
    {
        // 注意：这里需要从TriggerPool获取池使用率数据
        // 以下代码仅为示例，实际实现需要根据TriggerPool提供的API调整
        
        // TODO: 从TriggerPool获取使用率数据
        float usageRate = 0.5f; // 示例值
        
        var pool = TriggerPool.Instance;
        
        // 当使用率高时扩容
        if (usageRate > _expandThreshold)
        {
            int currentSize = 100; // 示例值，实际应从池获取
            int newSize = Mathf.RoundToInt(currentSize * 1.5f); // 扩容50%
            
            pool.SetMaxPoolSize(newSize);
            Debug.Log($"触发器池使用率高 ({usageRate:P0})，扩容至 {newSize}");
        }
        // 当使用率低时裁剪
        else if (usageRate < _trimThreshold)
        {
            int currentSize = 100; // 示例值，实际应从池获取
            int newSize = Mathf.Max(50, Mathf.RoundToInt(currentSize * 0.7f)); // 裁剪30%，但不小于50
            
            pool.SetMaxPoolSize(newSize);
            Debug.Log($"触发器池使用率低 ({usageRate:P0})，裁剪至 {newSize}");
        }
    }
}
```

## 在战斗管理器中使用对象池

以下示例展示如何在战斗管理器中配置不同战斗场景的对象池大小：

```csharp
using UnityEngine;
using TriggerSystem;

public class CombatManager : MonoBehaviour
{
    public enum CombatScale
    {
        Small,  // 小规模战斗（1-3个单位）
        Medium, // 中等规模战斗（4-8个单位）
        Large   // 大规模战斗（9+个单位）
    }
    
    [SerializeField] private CombatScale _combatScale = CombatScale.Medium;
    
    private void Start()
    {
        // 根据战斗规模配置触发器池
        ConfigureTriggerPoolForCombat();
    }
    
    private void ConfigureTriggerPoolForCombat()
    {
        var pool = TriggerPool.Instance;
        
        switch (_combatScale)
        {
            case CombatScale.Small:
                // 小规模战斗配置
                pool.SetMaxPoolSize(80);
                pool.SetPreAllocateCount(10);
                pool.SetExpandCount(5);
                break;
                
            case CombatScale.Medium:
                // 中等规模战斗配置
                pool.SetMaxPoolSize(150);
                pool.SetPreAllocateCount(20);
                pool.SetExpandCount(10);
                break;
                
            case CombatScale.Large:
                // 大规模战斗配置
                pool.SetMaxPoolSize(250);
                pool.SetPreAllocateCount(40);
                pool.SetExpandCount(20);
                break;
        }
        
        Debug.Log($"触发器池已为{_combatScale}规模战斗配置");
    }
    
    private void OnDestroy()
    {
        // 战斗结束后可以重置池大小到默认值
        if (TriggerPool.Instance != null)
        {
            TriggerPool.Instance.SetMaxPoolSize(100);
            Debug.Log("战斗结束，触发器池已重置为默认大小");
        }
    }
}
```

## 性能优化场景的示例

在性能关键的场景中使用触发器池：

```csharp
using UnityEngine;
using TriggerSystem;

public class BossEncounter : MonoBehaviour
{
    [SerializeField] private bool _optimizeForPerformance = true;
    
    private void Start()
    {
        if (_optimizeForPerformance)
        {
            OptimizeTriggerPoolForBoss();
        }
    }
    
    private void OptimizeTriggerPoolForBoss()
    {
        var pool = TriggerPool.Instance;
        
        // Boss战斗优化
        
        // 1. 增加池大小，避免动态分配
        pool.SetMaxPoolSize(200);
        
        // 2. 预分配大量对象，确保关键时刻不需要动态创建
        pool.SetPreAllocateCount(40);
        
        // 3. 禁用详细日志，减少日志开销
        pool.SetVerboseLogging(false);
        
        
        // 4. 为大量技能触发器预留空间
        // 这里假设有一个方法可以为特定类型预分配对象
        // 实际使用时需要根据TriggerPool的API调整
        PreAllocateSkillTriggers(50);
        
        Debug.Log("触发器池已为Boss战斗优化");
    }
    
    // 假设的预分配方法
    private void PreAllocateSkillTriggers(int count)
    {
        // 实际应根据TriggerPool提供的API实现
        Debug.Log($"为技能触发器预分配{count}个对象");
    }
    
    private void OnDestroy()
    {
        // 战斗结束后记录统计信息，用于分析
        if (TriggerPool.Instance != null)
        {
            string stats = TriggerPool.Instance.GetPoolStats();
            Debug.Log($"Boss战斗触发器池使用统计:\n{stats}");
        }
    }
}
```

## 最佳实践摘要

1. **预分配合理数量**：
   - 分析游戏中最常用的触发器类型
   - 为这些类型预分配足够数量的对象
   - 一般建议比预期峰值使用量多10-20%

2. **监控和调整**：
   - 定期检查对象池统计信息
   - 根据实际使用情况调整池大小
   - 关注复用率指标，应保持在70%以上

3. **场景优化**：
   - 不同场景使用不同的对象池配置
   - 战斗开始前增加预分配
   - 战斗结束后可以裁剪池大小

4. **避免泄漏**：
   - 确保触发器使用完毕后及时返回对象池
   - 不要长时间持有触发器引用
   - 场景转换时检查是否有未返回的触发器 