# GameConfig系统集成技术文档

## 概述

本文档详细说明了如何将GameConfig配置工具集成到现有的Unity动作游戏项目中，实现数据驱动的游戏开发模式。

### 项目背景

当前项目包含以下主要系统：
- **技能系统**：基于SkillBase的技能框架，支持帧数据驱动和自定义逻辑
- **Buff系统**：基于ScriptableObject的Buff管理系统
- **单位系统**：包含角色属性、关系管理等
- **触发器系统**：事件驱动的游戏逻辑触发机制

### 集成目标

1. **数据外部化**：将硬编码的配置数据迁移到Excel表格
2. **策划友好**：提供策划可直接编辑的配置界面
3. **类型安全**：保持强类型的代码访问方式
4. **性能优化**：实现高效的配置数据加载和访问
5. **系统兼容**：与现有系统无缝集成

## 1. GameConfig工作原理

### 1.1 数据流程图

```mermaid
graph TD
    A[Excel配置表] --> B[GameConfig生成器]
    B --> C[C#数据模型类]
    B --> D[序列化数据文件]
    C --> E[ConfigMgr管理类]
    D --> E
    E --> F[游戏运行时]

    subgraph "配置表结构"
        G[第1行: 表名/格式/继承]
        H[第2行: 字段名#类型]
        I[第3行: 特殊类型标记]
        J[第4行: 主键标记]
        K[第5行: 生成目标]
        L[第6行: 默认值]
        M[第7行: 注释说明]
    end

    A --> G
```

### 1.2 核心特性

#### 1.2.1 表格格式支持
- **横表(Horizontal)**：传统的行记录表格
- **纵表(Vertical)**：键值对形式的配置表
- **枚举表(Enum)**：枚举定义表
- **继承表**：支持面向对象的表继承关系

#### 1.2.2 数据类型支持
- **基础类型**：int, float, string, bool
- **数组类型**：int[], float[], string[]等，支持多维数组
- **枚举类型**：enum#EnumName格式
- **表连接**：link#TableName实现表间关联
- **多主键**：支持复合主键索引

#### 1.2.3 生成产物
- **数据模型类**：强类型的C#配置项类
- **管理器类**：ConfigMgr统一配置访问入口
- **枚举定义**：自动生成的枚举类型
- **数据文件**：序列化的配置数据

## 2. 项目集成架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "配置层"
        A[Excel配置表]
        B[GameConfig生成器]
        C[生成的配置代码]
        D[配置数据文件]
    end

    subgraph "适配层"
        E[SkillConfigAdapter]
        F[BuffConfigAdapter]
        G[UnitConfigAdapter]
        H[TriggerConfigAdapter]
    end

    subgraph "系统层"
        I[SkillSystem]
        J[BuffSystem]
        K[UnitSystem]
        L[TriggerSystem]
    end

    A --> B
    B --> C
    B --> D
    C --> E
    C --> F
    C --> G
    C --> H
    E --> I
    F --> J
    G --> K
    H --> L
```

### 2.2 配置表设计规范

#### 2.2.1 命名规范
- **表名**：使用PascalCase，如`SkillConfig`、`BuffConfig`
- **字段名**：使用camelCase，如`skillId`、`buffName`
- **枚举**：使用PascalCase，如`SkillType`、`BuffTag`

#### 2.2.2 目录结构
```
Config/
├── 技能系统/
│   ├── SkillConfig.xlsx
│   ├── SkillTypeEnum.xlsx
│   └── SkillEffectConfig.xlsx
├── Buff系统/
│   ├── BuffConfig.xlsx
│   ├── BuffTagEnum.xlsx
│   └── BuffEffectConfig.xlsx
├── 单位系统/
│   ├── UnitConfig.xlsx
│   ├── AttributeConfig.xlsx
│   └── RelationshipEnum.xlsx
└── 触发器系统/
    ├── TriggerConfig.xlsx
    └── TriggerTypeEnum.xlsx
```

## 3. 技能系统配置设计

### 3.1 技能配置表(SkillConfig.xlsx)

#### 3.1.1 表格结构

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| skillId#int | int | 技能ID(主键) | 1001 |
| skillName#string | string | 技能名称 | "火球术" |
| skillType#enum | enum#SkillType | 技能类型 | Magic |
| category#enum | enum#SkillCategory | 技能类别 | Attack |
| framesDuration#int | int | 技能持续帧数 | 60 |
| postMoveDuration#int | int | 后摇帧数 | 30 |
| cooldownTime#float | float | 冷却时间(秒) | 5.0 |
| manaCost#int | int | 法力消耗 | 20 |
| damage#float | float | 基础伤害 | 100.0 |
| hitBoxData#string | string | 攻击盒数据 | "Normal\|1.0\|0.5" |
| effectPath#string | string | 特效路径 | "Effects/Fireball" |
| animationClip#string | string | 动画片段 | "Skill_Fireball" |

#### 3.1.2 技能类型枚举(SkillTypeEnum.xlsx)

```
SkillType    Enum
Physical     1     物理技能
Magic        2     魔法技能
Hybrid       3     混合技能
```

### 3.2 技能配置适配器

```csharp
public static class SkillConfigAdapter
{
    public static void LoadSkillFromConfig(Unit unit, int skillConfigID)
    {
        var config = ConfigMgr.SkillConfig.Get(skillConfigID);
        if (config == null)
        {
            GameLogManager.Log($"未找到技能配置: {skillConfigID}", "SkillConfig", GameLogManager.LogType.Error);
            return;
        }

        // 创建技能实例
        var skillGO = new GameObject($"Skill_{config.SkillName}");
        skillGO.transform.SetParent(unit.transform);

        var skill = skillGO.AddComponent<SkillBase>();

        // 应用配置数据
        skill.skillID = config.SkillId.ToString();
        skill.skillName = config.SkillName;
        skill.category = config.Category;
        skill.skillFramesDuration = config.FramesDuration;
        skill.postMoveDuration = config.PostMoveDuration;

        // 解析攻击盒数据
        if (!string.IsNullOrEmpty(config.HitBoxData))
        {
            ParseHitBoxData(skill, config.HitBoxData);
        }

        // 加载特效和动画
        LoadSkillAssets(skill, config);

        unit.AddSkill(skill);
        GameLogManager.Log($"成功加载技能: {config.SkillName}", "SkillConfig");
    }

    private static void ParseHitBoxData(SkillBase skill, string hitBoxData)
    {
        // 解析攻击盒数据格式: "SizeType|DamageMultiplier|HitStunDuration"
        var parts = hitBoxData.Split('|');
        if (parts.Length >= 3)
        {
            var hitData = new HitBox_Data(
                skill.skillID,
                0.1f, // triggerInterval
                1,    // hitCount
                UnitRelationship.Enemy,
                parts[0], // attackBoxSizeType
                float.Parse(parts[1]), // damageMultiplier
                float.Parse(parts[2])  // hitStunDuration
            );

            skill.attackHitDatas = new HitBox_Data[] { hitData };
        }
    }

    private static void LoadSkillAssets(SkillBase skill, SkillConfigItem config)
    {
        // 加载特效资源
        if (!string.IsNullOrEmpty(config.EffectPath))
        {
            // 这里可以实现特效资源的加载逻辑
            GameLogManager.Log($"加载技能特效: {config.EffectPath}", "SkillConfig");
        }

        // 加载动画资源
        if (!string.IsNullOrEmpty(config.AnimationClip))
        {
            // 这里可以实现动画资源的加载逻辑
            GameLogManager.Log($"加载技能动画: {config.AnimationClip}", "SkillConfig");
        }
    }
}
```

## 4. Buff系统配置设计

### 4.1 Buff配置表(BuffConfig.xlsx)

#### 4.1.1 表格结构

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| buffId#string | string | BuffID(主键) | "buff_fire_damage" |
| buffName#string | string | Buff名称 | "燃烧伤害" |
| buffTag#enum | enum#BuffTag | Buff标签 | Damage |
| duration#float | float | 持续时间 | 10.0 |
| isPermanent#bool | bool | 是否永久 | false |
| mutilAddType#enum | enum#BuffMutilAddType | 重复添加方式 | multipleLayer |
| removeOneLayerOnTimeUp#bool | bool | 时间到时是否只移除一层 | true |
| iconPath#string | string | 图标路径 | "Icons/Buffs/Fire" |
| effectValue#float | float | 效果数值 | 5.0 |
| tickInterval#float | float | 周期间隔 | 1.0 |
```

### 4.2 Buff配置适配器

```csharp
public static class BuffConfigAdapter
{
    public static void CreateBuffFromConfig(string buffId, BuffHandler target, GameObject caster)
    {
        var config = ConfigMgr.BuffConfig.Get(buffId);
        if (config == null)
        {
            GameLogManager.Log($"未找到Buff配置: {buffId}", "BuffConfig", GameLogManager.LogType.Error);
            return;
        }

        // 创建配置驱动的Buff实例
        var buff = ScriptableObject.CreateInstance<ConfigDrivenBuff>();
        buff.InitializeFromConfig(config);

        // 添加到目标
        target.AddBuff(buffId, caster);

        GameLogManager.Log($"成功创建Buff: {config.BuffName}", "BuffConfig");
    }
}

// 配置驱动的Buff类
public class ConfigDrivenBuff : Buff
{
    private BuffConfigItem config;

    public void InitializeFromConfig(BuffConfigItem configItem)
    {
        config = configItem;

        // 应用配置数据
        buffName = config.BuffName;
        duration = config.Duration;
        isPermanent = config.IsPermanent;
        mutilAddType = config.MutilAddType;
        removeOneLayerOnTimeUp = config.RemoveOneLayerOnTimeUp;

        // 加载图标
        if (!string.IsNullOrEmpty(config.IconPath))
        {
            icon = Resources.Load<Sprite>(config.IconPath);
        }
    }

    public override void OnBuffStart()
    {
        GameLogManager.Log($"Buff开始: {config.BuffName}", "BuffConfig");

        // 根据配置启动周期效果
        if (config.TickInterval > 0)
        {
            StartBuffTickEffect(config.TickInterval);
        }
    }

    protected override void OnBuffTickEffect()
    {
        // 根据配置执行周期效果
        ApplyBuffEffect();
    }

    public override void OnBuffRemove()
    {
        GameLogManager.Log($"Buff移除: {config.BuffName}", "BuffConfig");
        RemoveBuffEffect();
    }

    public override void OnBuffModifyLayer(int change)
    {
        GameLogManager.Log($"Buff层数变化: {config.BuffName}, 变化: {change}", "BuffConfig");
    }

    public override void Reset()
    {
        // 重置Buff状态
    }

    private void ApplyBuffEffect()
    {
        // 根据配置应用效果
        if (Target?.Target != null)
        {
            // 这里可以根据config.EffectValue应用具体效果
            GameLogManager.Log($"应用Buff效果: {config.BuffName}, 数值: {config.EffectValue}", "BuffConfig");
        }
    }

    private void RemoveBuffEffect()
    {
        // 移除效果
    }
}

## 5. 单位系统配置设计

### 5.1 单位配置表(UnitConfig.xlsx)

#### 5.1.1 表格结构

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| unitId#string | string | 单位ID(主键) | "player_warrior" |
| unitName#string | string | 单位名称 | "战士" |
| prefabPath#string | string | 预制体路径 | "Units/Player/Warrior" |
| baseHealth#float | float | 基础生命值 | 1000.0 |
| baseAttack#float | float | 基础攻击力 | 100.0 |
| baseDefense#float | float | 基础防御力 | 50.0 |
| moveSpeed#float | float | 移动速度 | 5.0 |
| faction#enum | enum#UnitFaction | 阵营 | Player |
| skillIds#int[] | int[] | 技能ID列表 | 1001\|1002\|1003 |
| initialBuffs#string[] | string[] | 初始Buff列表 | buff_health_regen\|buff_armor |

### 5.2 单位配置适配器

```csharp
public static class UnitConfigAdapter
{
    public static void ApplyConfigToUnit(Unit unit, string unitConfigId)
    {
        var config = ConfigMgr.UnitConfig.Get(unitConfigId);
        if (config == null)
        {
            GameLogManager.Log($"未找到单位配置: {unitConfigId}", "UnitConfig", GameLogManager.LogType.Error);
            return;
        }

        // 应用基础属性
        ApplyBaseAttributes(unit, config);

        // 加载技能
        LoadUnitSkills(unit, config.SkillIds);

        // 应用初始Buff
        ApplyInitialBuffs(unit, config.InitialBuffs);

        GameLogManager.Log($"成功应用单位配置: {config.UnitName}", "UnitConfig");
    }

    private static void ApplyBaseAttributes(Unit unit, UnitConfigItem config)
    {
        // 设置基础属性
        var healthAttr = unit.GetAttribute("Health_Attribute");
        if (healthAttr != null)
        {
            healthAttr.SetBaseValue(config.BaseHealth);
        }

        var attackAttr = unit.GetAttribute("Atk_Attribute");
        if (attackAttr != null)
        {
            attackAttr.SetBaseValue(config.BaseAttack);
        }

        var defenseAttr = unit.GetAttribute("Def_Attribute");
        if (defenseAttr != null)
        {
            defenseAttr.SetBaseValue(config.BaseDefense);
        }

        var speedAttr = unit.GetAttribute("MoveSpeed_Attribute");
        if (speedAttr != null)
        {
            speedAttr.SetBaseValue(config.MoveSpeed);
        }

        // 设置单位名称
        unit.unitName = config.UnitName;
    }

    private static void LoadUnitSkills(Unit unit, int[] skillIds)
    {
        if (skillIds == null || skillIds.Length == 0) return;

        foreach (var skillId in skillIds)
        {
            SkillConfigAdapter.LoadSkillFromConfig(unit, skillId);
        }
    }

    private static void ApplyInitialBuffs(Unit unit, string[] buffIds)
    {
        if (buffIds == null || buffIds.Length == 0) return;

        var buffHandler = unit.GetComponent<BuffHandler>();
        if (buffHandler == null) return;

        foreach (var buffId in buffIds)
        {
            BuffConfigAdapter.CreateBuffFromConfig(buffId, buffHandler, unit.gameObject);
        }
    }
}
```

## 6. 触发器系统配置设计

### 6.1 触发器配置表(TriggerConfig.xlsx)

#### 6.1.1 表格结构

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| triggerId#int | int | 触发器ID(主键) | 2001 |
| triggerName#string | string | 触发器名称 | "伤害触发器" |
| triggerType#enum | enum#TriggerType | 触发器类型 | Damage |
| eventType#enum | enum#EventType | 监听事件类型 | UnitDamaged |
| conditionType#enum | enum#ConditionType | 条件类型 | HealthBelow |
| conditionValue#float | float | 条件数值 | 0.5 |
| actionType#enum | enum#ActionType | 执行动作类型 | AddBuff |
| actionParams#string | string | 动作参数 | "buff_rage\|self" |
| cooldown#float | float | 冷却时间 | 5.0 |
| maxTriggerCount#int | int | 最大触发次数 | 3 |

### 6.2 触发器配置适配器

```csharp
public static class TriggerConfigAdapter
{
    public static void CreateTriggerFromConfig(Unit unit, int triggerConfigId)
    {
        var config = ConfigMgr.TriggerConfig.Get(triggerConfigId);
        if (config == null)
        {
            GameLogManager.Log($"未找到触发器配置: {triggerConfigId}", "TriggerConfig", GameLogManager.LogType.Error);
            return;
        }

        // 根据触发器类型创建对应的触发器
        switch (config.TriggerType)
        {
            case TriggerType.Damage:
                CreateDamageTrigger(unit, config);
                break;
            case TriggerType.Health:
                CreateHealthTrigger(unit, config);
                break;
            case TriggerType.Buff:
                CreateBuffTrigger(unit, config);
                break;
            default:
                GameLogManager.Log($"未支持的触发器类型: {config.TriggerType}", "TriggerConfig", GameLogManager.LogType.Warning);
                break;
        }
    }

    private static void CreateDamageTrigger(Unit unit, TriggerConfigItem config)
    {
        var trigger = unit.gameObject.AddComponent<ConfigDrivenDamageTrigger>();
        trigger.InitializeFromConfig(config);

        GameLogManager.Log($"创建伤害触发器: {config.TriggerName}", "TriggerConfig");
    }

    private static void CreateHealthTrigger(Unit unit, TriggerConfigItem config)
    {
        var trigger = unit.gameObject.AddComponent<ConfigDrivenHealthTrigger>();
        trigger.InitializeFromConfig(config);

        GameLogManager.Log($"创建生命值触发器: {config.TriggerName}", "TriggerConfig");
    }

    private static void CreateBuffTrigger(Unit unit, TriggerConfigItem config)
    {
        var trigger = unit.gameObject.AddComponent<ConfigDrivenBuffTrigger>();
        trigger.InitializeFromConfig(config);

        GameLogManager.Log($"创建Buff触发器: {config.TriggerName}", "TriggerConfig");
    }
}

// 配置驱动的伤害触发器
public class ConfigDrivenDamageTrigger : MonoBehaviour
{
    private TriggerConfigItem config;
    private float lastTriggerTime;
    private int triggerCount;

    public void InitializeFromConfig(TriggerConfigItem configItem)
    {
        config = configItem;
        lastTriggerTime = 0f;
        triggerCount = 0;

        // 注册事件监听
        RegisterEventListener();
    }

    private void RegisterEventListener()
    {
        var eventListener = GetComponent<EventListenerComponent>();
        if (eventListener == null)
        {
            eventListener = gameObject.AddComponent<EventListenerComponent>();
        }

        // 根据配置的事件类型注册监听
        switch (config.EventType)
        {
            case EventType.UnitDamaged:
                eventListener.RegisterUnitDamagedListener(OnUnitDamaged);
                break;
            // 可以添加更多事件类型
        }
    }

    private void OnUnitDamaged(Unit damagedUnit, float damage, GameObject attacker)
    {
        // 检查冷却时间
        if (Time.time - lastTriggerTime < config.Cooldown) return;

        // 检查触发次数限制
        if (config.MaxTriggerCount > 0 && triggerCount >= config.MaxTriggerCount) return;

        // 检查条件
        if (!CheckCondition(damagedUnit, damage)) return;

        // 执行动作
        ExecuteAction(damagedUnit, attacker);

        // 更新触发状态
        lastTriggerTime = Time.time;
        triggerCount++;

        GameLogManager.Log($"触发器执行: {config.TriggerName}", "TriggerConfig");
    }

    private bool CheckCondition(Unit unit, float damage)
    {
        switch (config.ConditionType)
        {
            case ConditionType.HealthBelow:
                var healthAttr = unit.GetAttribute("Health_Attribute");
                var maxHealthAttr = unit.GetAttribute("Health_Max_Attribute");
                if (healthAttr != null && maxHealthAttr != null)
                {
                    float healthPercent = healthAttr.Value / maxHealthAttr.Value;
                    return healthPercent <= config.ConditionValue;
                }
                break;
            case ConditionType.DamageAbove:
                return damage >= config.ConditionValue;
                break;
            // 可以添加更多条件类型
        }

        return true; // 默认通过
    }

    private void ExecuteAction(Unit targetUnit, GameObject attacker)
    {
        switch (config.ActionType)
        {
            case ActionType.AddBuff:
                ExecuteAddBuffAction(targetUnit, attacker);
                break;
            case ActionType.Heal:
                ExecuteHealAction(targetUnit);
                break;
            // 可以添加更多动作类型
        }
    }

    private void ExecuteAddBuffAction(Unit targetUnit, GameObject attacker)
    {
        // 解析动作参数: "buffId|target"
        var parts = config.ActionParams.Split('|');
        if (parts.Length >= 2)
        {
            string buffId = parts[0];
            string target = parts[1];

            Unit actualTarget = target == "self" ? targetUnit :
                               target == "attacker" ? attacker?.GetComponent<Unit>() : targetUnit;

            if (actualTarget != null)
            {
                var buffHandler = actualTarget.GetComponent<BuffHandler>();
                if (buffHandler != null)
                {
                    BuffConfigAdapter.CreateBuffFromConfig(buffId, buffHandler, gameObject);
                }
            }
        }
    }

    private void ExecuteHealAction(Unit targetUnit)
    {
        var healthAttr = targetUnit.GetAttribute("Health_Attribute");
        if (healthAttr != null)
        {
            healthAttr.AddModifier(config.ConditionValue, OHA.Attribute.AttributeModifierType.Additive);
            GameLogManager.Log($"治疗单位: {targetUnit.unitName}, 数值: {config.ConditionValue}", "TriggerConfig");
        }
    }
}
```

## 7. 实施步骤

### 7.1 环境准备

#### 7.1.1 安装GameConfig工具

1. **下载GameConfig**
   ```bash
   git clone https://github.com/gh-kL/GameConfig.git
   ```

2. **安装Node.js依赖**
   ```bash
   cd GameConfig/Config/Generator
   npm install
   ```

3. **配置生成器**
   - 编辑`Config/Generator/Config.json`
   - 设置输出路径和命名空间

#### 7.1.2 项目目录结构

```
OHA_Pro_CursorTest/
├── Assets/
│   ├── Scripts/
│   │   ├── GeneratedConfigs/     # 生成的配置代码
│   │   └── ConfigAdapters/       # 配置适配器
│   └── Resources/
│       └── Config/               # 配置数据文件
├── Config/                       # Excel配置表
│   ├── 技能系统/
│   ├── Buff系统/
│   ├── 单位系统/
│   └── 触发器系统/
└── GameConfig-main/              # GameConfig工具
```

### 7.2 配置表创建

#### 7.2.1 技能配置表示例

**SkillConfig.xlsx**
```
SkillConfig     Horizontal
skillId#int     skillName#string    skillType#enum      category#enum       framesDuration#int
int             string              enum#SkillType      enum#SkillCategory  int
技能ID          技能名称            技能类型            技能类别            持续帧数
1               1                   1                   1                   1
CS              C                   CS                  CS                  CS
1001            火球术              Magic               Attack              60
1002            治疗术              Magic               Support             30
1003            冲锋                Physical            Attack              45
```

#### 7.2.2 枚举表示例

**SkillTypeEnum.xlsx**
```
SkillType       Enum
Physical        1       物理技能
Magic           2       魔法技能
Hybrid          3       混合技能
```

### 7.3 代码生成

#### 7.3.1 执行生成命令

```bash
cd Config
node Generator/dist/main.js
# 或者双击 gen.bat
```

#### 7.3.2 验证生成结果

检查以下文件是否正确生成：
- `Assets/Scripts/GeneratedConfigs/SkillConfigItem.cs`
- `Assets/Scripts/GeneratedConfigs/ConfigMgr.cs`
- `Assets/Scripts/GeneratedConfigs/SkillType.cs`
- `Assets/Resources/Config/Config.txt`

### 7.4 适配器实现

#### 7.4.1 创建配置适配器基类

```csharp
public abstract class ConfigAdapterBase
{
    protected static void LogError(string message, string tag = "ConfigAdapter")
    {
        GameLogManager.Log(message, tag, GameLogManager.LogType.Error);
    }

    protected static void LogInfo(string message, string tag = "ConfigAdapter")
    {
        GameLogManager.Log(message, tag, GameLogManager.LogType.Info);
    }

    protected static void LogWarning(string message, string tag = "ConfigAdapter")
    {
        GameLogManager.Log(message, tag, GameLogManager.LogType.Warning);
    }
}
```

#### 7.4.2 实现具体适配器

按照前面章节的示例实现各个系统的配置适配器。

### 7.5 系统集成

#### 7.5.1 配置管理器初始化

```csharp
public class GameConfigManager : MonoBehaviour
{
    public static GameConfigManager Instance { get; private set; }

    [Header("配置设置")]
    public string configPath = "Config/Config";

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeConfigs();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void InitializeConfigs()
    {
        try
        {
            // 初始化GameConfig
            ConfigMgr.Init(configPath);

            GameLogManager.Log("配置系统初始化成功", "GameConfig");
        }
        catch (System.Exception e)
        {
            GameLogManager.Log($"配置系统初始化失败: {e.Message}", "GameConfig", GameLogManager.LogType.Error);
        }
    }

    public void ReloadConfigs()
    {
        InitializeConfigs();
        GameLogManager.Log("配置重新加载完成", "GameConfig");
    }
}
```

#### 7.5.2 单位配置应用

```csharp
public class ConfigurableUnit : Unit
{
    [Header("配置")]
    public string unitConfigId;

    protected override void Start()
    {
        base.Start();

        if (!string.IsNullOrEmpty(unitConfigId))
        {
            UnitConfigAdapter.ApplyConfigToUnit(this, unitConfigId);
        }
    }
}
```

## 8. 最佳实践

### 8.1 配置表设计原则

#### 8.1.1 数据规范化
- **单一职责**：每个表只负责一类数据
- **避免冗余**：通过表连接减少数据重复
- **类型安全**：使用枚举而非魔法数字

#### 8.1.2 命名规范
- **表名**：使用PascalCase，如`SkillConfig`
- **字段名**：使用camelCase，如`skillId`
- **枚举值**：使用PascalCase，如`Physical`

#### 8.1.3 版本控制
- **Excel文件**：纳入版本控制
- **生成代码**：纳入版本控制
- **数据文件**：根据项目需要决定是否纳入

### 8.2 性能优化

#### 8.2.1 数据加载优化
```csharp
public class OptimizedConfigLoader
{
    private static Dictionary<string, object> configCache = new Dictionary<string, object>();

    public static T GetConfig<T>(string configId) where T : class
    {
        string cacheKey = $"{typeof(T).Name}_{configId}";

        if (configCache.TryGetValue(cacheKey, out object cached))
        {
            return cached as T;
        }

        // 加载配置并缓存
        T config = LoadConfigFromSource<T>(configId);
        if (config != null)
        {
            configCache[cacheKey] = config;
        }

        return config;
    }

    private static T LoadConfigFromSource<T>(string configId) where T : class
    {
        // 实际的配置加载逻辑
        return null;
    }
}
```

#### 8.2.2 内存管理
- **按需加载**：只加载当前需要的配置
- **缓存策略**：合理使用缓存避免重复加载
- **资源释放**：及时释放不再使用的配置数据

### 8.3 错误处理

#### 8.3.1 配置验证
```csharp
public static class ConfigValidator
{
    public static bool ValidateSkillConfig(SkillConfigItem config)
    {
        if (config == null)
        {
            GameLogManager.Log("技能配置为空", "ConfigValidator", GameLogManager.LogType.Error);
            return false;
        }

        if (config.FramesDuration <= 0)
        {
            GameLogManager.Log($"技能 {config.SkillName} 的持续帧数无效: {config.FramesDuration}",
                             "ConfigValidator", GameLogManager.LogType.Error);
            return false;
        }

        if (config.Damage < 0)
        {
            GameLogManager.Log($"技能 {config.SkillName} 的伤害值无效: {config.Damage}",
                             "ConfigValidator", GameLogManager.LogType.Warning);
        }

        return true;
    }
}
```

#### 8.3.2 降级处理
```csharp
public static class ConfigFallback
{
    public static SkillConfigItem GetSkillConfigWithFallback(int skillId)
    {
        var config = ConfigMgr.SkillConfig.Get(skillId);

        if (config == null)
        {
            GameLogManager.Log($"未找到技能配置 {skillId}，使用默认配置",
                             "ConfigFallback", GameLogManager.LogType.Warning);
            return CreateDefaultSkillConfig(skillId);
        }

        return config;
    }

    private static SkillConfigItem CreateDefaultSkillConfig(int skillId)
    {
        return new SkillConfigItem
        {
            SkillId = skillId,
            SkillName = "默认技能",
            FramesDuration = 60,
            Damage = 10.0f,
            // 其他默认值...
        };
    }
}
```

## 9. 测试与验证

### 9.1 单元测试

#### 9.1.1 配置加载测试

```csharp
[Test]
public void TestSkillConfigLoading()
{
    // 初始化配置
    ConfigMgr.Init("Config/Config");

    // 测试技能配置加载
    var skillConfig = ConfigMgr.SkillConfig.Get(1001);
    Assert.IsNotNull(skillConfig);
    Assert.AreEqual("火球术", skillConfig.SkillName);
    Assert.AreEqual(SkillType.Magic, skillConfig.SkillType);
}

[Test]
public void TestBuffConfigLoading()
{
    // 测试Buff配置加载
    var buffConfig = ConfigMgr.BuffConfig.Get("buff_fire_damage");
    Assert.IsNotNull(buffConfig);
    Assert.AreEqual("燃烧伤害", buffConfig.BuffName);
    Assert.AreEqual(10.0f, buffConfig.Duration);
}
```

#### 9.1.2 适配器测试

```csharp
[Test]
public void TestSkillConfigAdapter()
{
    var unit = CreateTestUnit();

    // 测试技能配置适配
    SkillConfigAdapter.LoadSkillFromConfig(unit, 1001);

    var skills = unit.GetComponents<SkillBase>();
    Assert.AreEqual(1, skills.Length);
    Assert.AreEqual("1001", skills[0].skillID);
    Assert.AreEqual("火球术", skills[0].skillName);
}
```

### 9.2 集成测试

#### 9.2.1 完整流程测试

```csharp
[Test]
public void TestCompleteConfigFlow()
{
    // 1. 初始化配置系统
    var configManager = CreateTestConfigManager();

    // 2. 创建单位并应用配置
    var unit = CreateTestUnit();
    UnitConfigAdapter.ApplyConfigToUnit(unit, "player_warrior");

    // 3. 验证属性设置
    var healthAttr = unit.GetAttribute("Health_Attribute");
    Assert.IsNotNull(healthAttr);
    Assert.AreEqual(1000.0f, healthAttr.Value);

    // 4. 验证技能加载
    var skills = unit.GetComponents<SkillBase>();
    Assert.IsTrue(skills.Length > 0);

    // 5. 验证Buff应用
    var buffHandler = unit.GetComponent<BuffHandler>();
    Assert.IsNotNull(buffHandler);
}
```

### 9.3 性能测试

#### 9.3.1 配置加载性能

```csharp
[Test]
public void TestConfigLoadingPerformance()
{
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

    // 加载大量配置
    for (int i = 0; i < 1000; i++)
    {
        var config = ConfigMgr.SkillConfig.Get(1001);
    }

    stopwatch.Stop();
    Assert.Less(stopwatch.ElapsedMilliseconds, 100); // 应在100ms内完成
}
```

## 10. 故障排除

### 10.1 常见问题

#### 10.1.1 配置加载失败

**问题**：ConfigMgr.Init()抛出异常
**解决方案**：
1. 检查配置文件路径是否正确
2. 确认Resources目录下存在Config.txt文件
3. 验证配置文件格式是否正确

#### 10.1.2 生成代码编译错误

**问题**：生成的C#代码无法编译
**解决方案**：
1. 检查Excel表格格式是否符合规范
2. 确认字段类型定义正确
3. 验证枚举定义是否完整

#### 10.1.3 配置数据不匹配

**问题**：运行时获取的配置数据与Excel中不一致
**解决方案**：
1. 重新生成配置文件
2. 检查Excel表格数据是否保存
3. 确认生成器配置正确

### 10.2 调试技巧

#### 10.2.1 配置数据验证

```csharp
public static class ConfigDebugger
{
    [MenuItem("Tools/Config/Validate All Configs")]
    public static void ValidateAllConfigs()
    {
        ConfigMgr.Init("Config/Config");

        // 验证技能配置
        ValidateSkillConfigs();

        // 验证Buff配置
        ValidateBuffConfigs();

        // 验证单位配置
        ValidateUnitConfigs();

        Debug.Log("配置验证完成");
    }

    private static void ValidateSkillConfigs()
    {
        foreach (var skill in ConfigMgr.SkillConfig.Data.Values)
        {
            if (!ConfigValidator.ValidateSkillConfig(skill))
            {
                Debug.LogError($"技能配置验证失败: {skill.SkillId}");
            }
        }
    }
}
```

## 11. 总结与展望

### 11.1 集成收益

通过GameConfig系统的集成，项目获得了以下收益：

1. **开发效率提升**
   - 策划可直接编辑Excel配置，无需程序员介入
   - 配置修改即时生效，缩短迭代周期
   - 自动化代码生成减少手工编码错误

2. **系统可维护性增强**
   - 配置与代码分离，降低系统耦合度
   - 强类型访问保证编译期安全
   - 统一的配置管理简化系统架构

3. **团队协作优化**
   - 明确的职责分工：程序负责逻辑，策划负责数值
   - 版本控制友好的配置管理
   - 标准化的配置流程

### 11.2 后续优化方向

#### 11.2.1 功能扩展
- **热更新支持**：实现配置的运行时热更新
- **可视化编辑器**：开发Unity内置的配置编辑器
- **配置校验增强**：添加更多数据完整性检查

#### 11.2.2 性能优化
- **按需加载**：实现配置的懒加载机制
- **内存优化**：优化配置数据的内存占用
- **加载速度**：提升大量配置的加载性能

#### 11.2.3 工具链完善
- **自动化测试**：集成配置的自动化测试
- **文档生成**：自动生成配置文档
- **版本管理**：配置版本的自动化管理

### 11.3 最终建议

1. **渐进式迁移**：建议分阶段将现有硬编码配置迁移到GameConfig系统
2. **团队培训**：确保团队成员熟悉新的配置流程
3. **持续监控**：监控配置系统的性能和稳定性
4. **文档维护**：保持配置文档的及时更新

通过本技术文档的指导，团队可以成功地将GameConfig系统集成到现有项目中，实现数据驱动的游戏开发模式，提升开发效率和产品质量。

---

# GameConfig API逻辑配置扩展方案

## 12. API逻辑配置系统设计

### 12.1 设计目标

实现配置与代码的彻底分离，不仅包括数值配置，还包括技能和Buff的逻辑配置，使策划能够通过Excel配置复杂的游戏逻辑。

### 12.2 核心架构

```mermaid
graph TB
    subgraph "配置层"
        A[Excel逻辑配置表]
        B[API动作配置表]
        C[条件配置表]
    end

    subgraph "解析层"
        D[LogicConfigParser]
        E[APIActionFactory]
        F[ConditionFactory]
    end

    subgraph "执行层"
        G[LogicExecutor]
        H[APIActionExecutor]
        I[ConditionEvaluator]
    end

    subgraph "系统层"
        J[SkillSystem]
        K[BuffSystem]
        L[TriggerSystem]
    end

    A --> D
    B --> E
    C --> F
    D --> G
    E --> H
    F --> I
    G --> J
    G --> K
    G --> L
```

### 12.3 配置表设计

#### 12.3.1 技能逻辑配置表(SkillLogicConfig.xlsx)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| skillId#int | int | 技能ID(主键) | 1001 |
| logicType#enum | enum#LogicType | 逻辑类型 | FrameAction |
| triggerFrame#int | int | 触发帧数 | 15 |
| triggerCondition#string | string | 触发条件 | "target.health < 0.5" |
| actionSequence#string[] | string[] | 动作序列 | AddBuff\|RemoveBuff\|PlayFX |
| actionParams#string[] | string[] | 动作参数 | buff_fire\|buff_shield\|explosion_fx |
| priority#int | int | 执行优先级 | 1 |

#### 12.3.2 API动作配置表(APIActionConfig.xlsx)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| actionId#string | string | 动作ID(主键) | "AddBuff_Fire" |
| actionType#enum | enum#APIActionType | 动作类型 | AddBuff |
| targetType#enum | enum#TargetType | 目标类型 | HitTarget |
| apiName#string | string | API方法名 | "AddBuff_API" |
| parameters#string | string | 参数配置 | "buffId:buff_fire;trackBuff:true" |
| executeFrame#int | int | 执行帧数 | 0 |
| conditions#string | string | 执行条件 | "target.faction == Enemy" |

#### 12.3.3 Buff逻辑配置表(BuffLogicConfig.xlsx)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| buffId#string | string | BuffID(主键) | "buff_fire_damage" |
| logicType#enum | enum#BuffLogicType | 逻辑类型 | OnStart |
| actionSequence#string[] | string[] | 动作序列 | AttributeModify\|PlayFX |
| actionParams#string[] | string[] | 动作参数 | health:-10\|fire_effect |
| tickInterval#float | float | 周期间隔 | 1.0 |
| conditions#string | string | 执行条件 | "layer > 1" |

### 12.4 逻辑解析器实现

#### 12.4.1 逻辑配置解析器

```csharp
public class LogicConfigParser
{
    private static Dictionary<string, ILogicAction> actionCache = new Dictionary<string, ILogicAction>();

    public static List<ILogicAction> ParseSkillLogic(int skillId)
    {
        var configs = ConfigMgr.SkillLogicConfig.GetBySkillId(skillId);
        var actions = new List<ILogicAction>();

        foreach (var config in configs.OrderBy(c => c.Priority))
        {
            var action = CreateLogicAction(config);
            if (action != null)
            {
                actions.Add(action);
            }
        }

        return actions;
    }

    private static ILogicAction CreateLogicAction(SkillLogicConfigItem config)
    {
        switch (config.LogicType)
        {
            case LogicType.FrameAction:
                return new FrameLogicAction(config);
            case LogicType.HitAction:
                return new HitLogicAction(config);
            case LogicType.StartAction:
                return new StartLogicAction(config);
            case LogicType.EndAction:
                return new EndLogicAction(config);
            default:
                GameLogManager.Log($"未支持的逻辑类型: {config.LogicType}", "LogicParser", GameLogManager.LogType.Error);
                return null;
        }
    }
}
```

#### 12.4.2 API动作工厂

```csharp
public class APIActionFactory
{
    private static Dictionary<APIActionType, Func<APIActionConfigItem, IAPIAction>> actionCreators =
        new Dictionary<APIActionType, Func<APIActionConfigItem, IAPIAction>>
        {
            { APIActionType.AddBuff, config => new AddBuffAPIAction(config) },
            { APIActionType.RemoveBuff, config => new RemoveBuffAPIAction(config) },
            { APIActionType.AttributeModify, config => new AttributeModifyAPIAction(config) },
            { APIActionType.PlayFX, config => new PlayFXAPIAction(config) },
            { APIActionType.DealDamage, config => new DealDamageAPIAction(config) },
            { APIActionType.Heal, config => new HealAPIAction(config) }
        };

    public static IAPIAction CreateAction(string actionId)
    {
        var config = ConfigMgr.APIActionConfig.Get(actionId);
        if (config == null)
        {
            GameLogManager.Log($"未找到API动作配置: {actionId}", "APIActionFactory", GameLogManager.LogType.Error);
            return null;
        }

        if (actionCreators.TryGetValue(config.ActionType, out var creator))
        {
            return creator(config);
        }

        GameLogManager.Log($"未支持的API动作类型: {config.ActionType}", "APIActionFactory", GameLogManager.LogType.Error);
        return null;
    }
}
```

### 12.5 逻辑动作实现

#### 12.5.1 逻辑动作接口

```csharp
public interface ILogicAction
{
    void Execute(LogicExecutionContext context);
    bool CanExecute(LogicExecutionContext context);
}

public interface IAPIAction
{
    void Execute(APIExecutionContext context);
    bool CanExecute(APIExecutionContext context);
}

public class LogicExecutionContext
{
    public SkillBase Skill { get; set; }
    public Unit Caster { get; set; }
    public Unit Target { get; set; }
    public HitEventData HitData { get; set; }
    public int CurrentFrame { get; set; }
    public Dictionary<string, object> Variables { get; set; } = new Dictionary<string, object>();
}

public class APIExecutionContext
{
    public GameObject Caster { get; set; }
    public GameObject Target { get; set; }
    public SkillBase Skill { get; set; }
    public BuffHandler BuffHandler { get; set; }
    public HitEventData HitData { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
}
```

#### 12.5.2 帧动作实现

```csharp
public class FrameLogicAction : ILogicAction
{
    private SkillLogicConfigItem config;
    private List<IAPIAction> apiActions;
    private ICondition condition;

    public FrameLogicAction(SkillLogicConfigItem config)
    {
        this.config = config;
        this.apiActions = ParseAPIActions(config.ActionSequence, config.ActionParams);
        this.condition = ConditionFactory.CreateCondition(config.TriggerCondition);
    }

    public void Execute(LogicExecutionContext context)
    {
        if (!CanExecute(context)) return;

        // 在指定帧执行动作
        if (context.Skill != null)
        {
            context.Skill.AddSkillFramesAction(config.TriggerFrame, () =>
            {
                ExecuteAPIActions(context);
            });
        }
    }

    public bool CanExecute(LogicExecutionContext context)
    {
        return condition?.Evaluate(context) ?? true;
    }

    private void ExecuteAPIActions(LogicExecutionContext context)
    {
        var apiContext = new APIExecutionContext
        {
            Caster = context.Caster?.gameObject,
            Target = context.Target?.gameObject,
            Skill = context.Skill
        };

        foreach (var action in apiActions)
        {
            try
            {
                action.Execute(apiContext);
            }
            catch (Exception e)
            {
                GameLogManager.Log($"执行API动作失败: {e.Message}", "FrameLogicAction", GameLogManager.LogType.Error);
            }
        }
    }

    private List<IAPIAction> ParseAPIActions(string[] actionSequence, string[] actionParams)
    {
        var actions = new List<IAPIAction>();

        for (int i = 0; i < actionSequence.Length && i < actionParams.Length; i++)
        {
            var actionId = $"{actionSequence[i]}_{actionParams[i]}";
            var action = APIActionFactory.CreateAction(actionId);
            if (action != null)
            {
                actions.Add(action);
            }
        }

        return actions;
    }
}
```

#### 12.5.3 AddBuff API动作实现

```csharp
public class AddBuffAPIAction : IAPIAction
{
    private APIActionConfigItem config;
    private Dictionary<string, string> parameters;
    private ICondition condition;

    public AddBuffAPIAction(APIActionConfigItem config)
    {
        this.config = config;
        this.parameters = ParseParameters(config.Parameters);
        this.condition = ConditionFactory.CreateCondition(config.Conditions);
    }

    public void Execute(APIExecutionContext context)
    {
        if (!CanExecute(context)) return;

        // 解析目标
        var target = ResolveTarget(context);
        if (target == null) return;

        // 获取参数
        string buffId = parameters.GetValueOrDefault("buffId", "");
        bool trackBuff = bool.Parse(parameters.GetValueOrDefault("trackBuff", "false"));

        // 执行AddBuff_API
        if (context.Skill != null)
        {
            context.Skill.AddBuff_API(
                target: target,
                buffId: buffId,
                caster: context.Caster,
                trackAddedBuff: trackBuff,
                executeAtFrame: config.ExecuteFrame
            );
        }
        else
        {
            // 直接调用CrossSystemAPI
            var buffHandler = CommonAPIUtility.GetOrAddBuffHandler(target);
            if (buffHandler != null)
            {
                CommonAPIUtility.AddBuff_Core(buffHandler, buffId, context.Caster, trackBuff);
            }
        }

        GameLogManager.Log($"执行AddBuff API: buffId={buffId}, target={target.name}", "AddBuffAPIAction");
    }

    public bool CanExecute(APIExecutionContext context)
    {
        return condition?.Evaluate(context) ?? true;
    }

    private GameObject ResolveTarget(APIExecutionContext context)
    {
        switch (config.TargetType)
        {
            case TargetType.Self:
                return context.Caster;
            case TargetType.HitTarget:
                return context.HitData?.target?.gameObject ?? context.Target;
            case TargetType.Specified:
                // 从参数中获取指定目标
                return context.Target;
            default:
                return context.Target;
        }
    }

    private Dictionary<string, string> ParseParameters(string paramString)
    {
        var result = new Dictionary<string, string>();
        if (string.IsNullOrEmpty(paramString)) return result;

        var pairs = paramString.Split(';');
        foreach (var pair in pairs)
        {
            var keyValue = pair.Split(':');
            if (keyValue.Length == 2)
            {
                result[keyValue[0].Trim()] = keyValue[1].Trim();
            }
        }

        return result;
    }
}
```

### 12.6 条件系统实现

#### 12.6.1 条件工厂

```csharp
public class ConditionFactory
{
    public static ICondition CreateCondition(string conditionString)
    {
        if (string.IsNullOrEmpty(conditionString)) return null;

        // 解析条件表达式
        var parser = new ConditionParser();
        return parser.Parse(conditionString);
    }
}

public interface ICondition
{
    bool Evaluate(object context);
}

public class ConditionParser
{
    public ICondition Parse(string expression)
    {
        // 简单的条件解析实现
        // 支持格式: "target.health < 0.5", "layer > 1", "target.faction == Enemy"

        if (expression.Contains("&&"))
        {
            var parts = expression.Split(new[] { "&&" }, StringSplitOptions.RemoveEmptyEntries);
            var conditions = parts.Select(p => Parse(p.Trim())).ToArray();
            return new AndCondition(conditions);
        }

        if (expression.Contains("||"))
        {
            var parts = expression.Split(new[] { "||" }, StringSplitOptions.RemoveEmptyEntries);
            var conditions = parts.Select(p => Parse(p.Trim())).ToArray();
            return new OrCondition(conditions);
        }

        // 单个条件解析
        return ParseSingleCondition(expression);
    }

    private ICondition ParseSingleCondition(string expression)
    {
        // 解析比较操作符
        string[] operators = { "<=", ">=", "==", "!=", "<", ">" };

        foreach (var op in operators)
        {
            if (expression.Contains(op))
            {
                var parts = expression.Split(new[] { op }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 2)
                {
                    return new ComparisonCondition(
                        parts[0].Trim(),
                        op,
                        parts[1].Trim()
                    );
                }
            }
        }

        return new TrueCondition(); // 默认为真
    }
}
```

#### 12.6.2 条件实现

```csharp
public class ComparisonCondition : ICondition
{
    private string leftExpression;
    private string operatorType;
    private string rightExpression;

    public ComparisonCondition(string left, string op, string right)
    {
        leftExpression = left;
        operatorType = op;
        rightExpression = right;
    }

    public bool Evaluate(object context)
    {
        var leftValue = EvaluateExpression(leftExpression, context);
        var rightValue = EvaluateExpression(rightExpression, context);

        return CompareValues(leftValue, operatorType, rightValue);
    }

    private object EvaluateExpression(string expression, object context)
    {
        // 解析表达式，支持属性访问
        if (expression.Contains("."))
        {
            return EvaluatePropertyAccess(expression, context);
        }

        // 尝试解析为数值
        if (float.TryParse(expression, out float floatValue))
        {
            return floatValue;
        }

        // 尝试解析为布尔值
        if (bool.TryParse(expression, out bool boolValue))
        {
            return boolValue;
        }

        // 字符串值
        return expression.Trim('"', '\'');
    }

    private object EvaluatePropertyAccess(string expression, object context)
    {
        var parts = expression.Split('.');
        object current = context;

        foreach (var part in parts)
        {
            if (current == null) return null;

            // 使用反射获取属性值
            var property = current.GetType().GetProperty(part,
                System.Reflection.BindingFlags.Public |
                System.Reflection.BindingFlags.Instance |
                System.Reflection.BindingFlags.IgnoreCase);

            if (property != null)
            {
                current = property.GetValue(current);
            }
            else
            {
                // 尝试获取字段
                var field = current.GetType().GetField(part,
                    System.Reflection.BindingFlags.Public |
                    System.Reflection.BindingFlags.Instance |
                    System.Reflection.BindingFlags.IgnoreCase);

                if (field != null)
                {
                    current = field.GetValue(current);
                }
                else
                {
                    return null;
                }
            }
        }

        return current;
    }

    private bool CompareValues(object left, string op, object right)
    {
        if (left == null || right == null) return false;

        switch (op)
        {
            case "==":
                return left.Equals(right);
            case "!=":
                return !left.Equals(right);
            case "<":
                return CompareNumeric(left, right) < 0;
            case "<=":
                return CompareNumeric(left, right) <= 0;
            case ">":
                return CompareNumeric(left, right) > 0;
            case ">=":
                return CompareNumeric(left, right) >= 0;
            default:
                return false;
        }
    }

    private int CompareNumeric(object left, object right)
    {
        if (left is IComparable leftComp && right is IComparable rightComp)
        {
            return leftComp.CompareTo(rightComp);
        }

        return 0;
    }
}

public class AndCondition : ICondition
{
    private ICondition[] conditions;

    public AndCondition(ICondition[] conditions)
    {
        this.conditions = conditions;
    }

    public bool Evaluate(object context)
    {
        return conditions.All(c => c.Evaluate(context));
    }
}

public class OrCondition : ICondition
{
    private ICondition[] conditions;

    public OrCondition(ICondition[] conditions)
    {
        this.conditions = conditions;
    }

    public bool Evaluate(object context)
    {
        return conditions.Any(c => c.Evaluate(context));
    }
}

public class TrueCondition : ICondition
{
    public bool Evaluate(object context) => true;
}
```

### 12.7 系统集成实现

#### 12.7.1 配置驱动的技能系统

```csharp
public class ConfigDrivenSkill : SkillBase
{
    private List<ILogicAction> configLogicActions;

    public override void SkillStartCustomizedLogicRegister()
    {
        base.SkillStartCustomizedLogicRegister();

        // 从配置加载逻辑
        LoadLogicFromConfig();

        // 执行开始逻辑
        ExecuteLogicActions(LogicType.StartAction);
    }

    public override void SkillFrameCustomizedLogicRegister()
    {
        base.SkillFrameCustomizedLogicRegister();

        // 执行帧逻辑
        ExecuteLogicActions(LogicType.FrameAction);
    }

    public override void SkillHitCustomizedLogicRegister()
    {
        base.SkillHitCustomizedLogicRegister();

        // 执行命中逻辑
        ExecuteLogicActions(LogicType.HitAction);
    }

    public override void SkillStopingCustomizedLogicRegister()
    {
        base.SkillStopingCustomizedLogicRegister();

        // 执行结束逻辑
        ExecuteLogicActions(LogicType.EndAction);
    }

    private void LoadLogicFromConfig()
    {
        if (int.TryParse(skillID, out int skillIdInt))
        {
            configLogicActions = LogicConfigParser.ParseSkillLogic(skillIdInt);
            GameLogManager.Log($"为技能 {skillID} 加载了 {configLogicActions.Count} 个逻辑动作", "ConfigDrivenSkill");
        }
    }

    private void ExecuteLogicActions(LogicType logicType)
    {
        if (configLogicActions == null) return;

        var context = new LogicExecutionContext
        {
            Skill = this,
            Caster = selfUnit,
            CurrentFrame = GetCurrentFrame()
        };

        var actionsToExecute = configLogicActions.Where(a =>
            a is FrameLogicAction frameAction && frameAction.LogicType == logicType ||
            a is HitLogicAction hitAction && hitAction.LogicType == logicType ||
            a is StartLogicAction startAction && startAction.LogicType == logicType ||
            a is EndLogicAction endAction && endAction.LogicType == logicType
        );

        foreach (var action in actionsToExecute)
        {
            try
            {
                action.Execute(context);
            }
            catch (Exception e)
            {
                GameLogManager.Log($"执行配置逻辑失败: {e.Message}", "ConfigDrivenSkill", GameLogManager.LogType.Error);
            }
        }
    }

    private int GetCurrentFrame()
    {
        if (skillStartTime <= 0) return 0;

        float elapsedTime = Time.time - skillStartTime;
        return Mathf.FloorToInt(elapsedTime * FrameRateManager.targetFrameRate) + 1;
    }
}
```

#### 12.7.2 配置驱动的Buff系统

```csharp
public class ConfigDrivenBuff : Buff
{
    private List<ILogicAction> configLogicActions;
    private BuffLogicConfigItem logicConfig;

    public void InitializeFromLogicConfig(string buffId)
    {
        var configs = ConfigMgr.BuffLogicConfig.GetByBuffId(buffId);
        configLogicActions = new List<ILogicAction>();

        foreach (var config in configs)
        {
            var action = CreateBuffLogicAction(config);
            if (action != null)
            {
                configLogicActions.Add(action);
            }
        }

        GameLogManager.Log($"为Buff {buffId} 加载了 {configLogicActions.Count} 个逻辑动作", "ConfigDrivenBuff");
    }

    public override void OnBuffStart()
    {
        ExecuteBuffLogic(BuffLogicType.OnStart);
    }

    protected override void OnBuffTickEffect()
    {
        ExecuteBuffLogic(BuffLogicType.OnTick);
    }

    public override void OnBuffRemove()
    {
        ExecuteBuffLogic(BuffLogicType.OnRemove);
    }

    public override void OnBuffModifyLayer(int change)
    {
        var context = new LogicExecutionContext
        {
            Caster = Caster?.GetComponent<Unit>(),
            Target = Target?.Target?.GetComponent<Unit>()
        };
        context.Variables["layerChange"] = change;
        context.Variables["currentLayer"] = Layer;

        ExecuteBuffLogic(BuffLogicType.OnLayerChange, context);
    }

    public override void Reset()
    {
        configLogicActions?.Clear();
    }

    private void ExecuteBuffLogic(BuffLogicType logicType, LogicExecutionContext context = null)
    {
        if (configLogicActions == null) return;

        if (context == null)
        {
            context = new LogicExecutionContext
            {
                Caster = Caster?.GetComponent<Unit>(),
                Target = Target?.Target?.GetComponent<Unit>()
            };
        }

        context.Variables["buffId"] = ID;
        context.Variables["buffLayer"] = Layer;
        context.Variables["remainingTime"] = RemainingTime;

        var actionsToExecute = configLogicActions.Where(a =>
            a is BuffLogicAction buffAction && buffAction.LogicType == logicType
        );

        foreach (var action in actionsToExecute)
        {
            try
            {
                action.Execute(context);
            }
            catch (Exception e)
            {
                GameLogManager.Log($"执行Buff配置逻辑失败: {e.Message}", "ConfigDrivenBuff", GameLogManager.LogType.Error);
            }
        }
    }

    private ILogicAction CreateBuffLogicAction(BuffLogicConfigItem config)
    {
        switch (config.LogicType)
        {
            case BuffLogicType.OnStart:
                return new BuffStartLogicAction(config);
            case BuffLogicType.OnTick:
                return new BuffTickLogicAction(config);
            case BuffLogicType.OnRemove:
                return new BuffRemoveLogicAction(config);
            case BuffLogicType.OnLayerChange:
                return new BuffLayerChangeLogicAction(config);
            default:
                GameLogManager.Log($"未支持的Buff逻辑类型: {config.LogicType}", "ConfigDrivenBuff", GameLogManager.LogType.Error);
                return null;
        }
    }
}
```

### 12.8 配置表示例

#### 12.8.1 火球术技能配置示例

**SkillLogicConfig.xlsx**
```
skillId  logicType    triggerFrame  triggerCondition  actionSequence        actionParams           priority
1001     FrameAction  15           ""                AddBuff|PlayFX        buff_fire|fireball_fx  1
1001     HitAction    0            "target.faction == Enemy"  DealDamage|AddBuff   100|buff_burn         2
1001     EndAction    0            ""                RemoveBuff            buff_fire              3
```

**APIActionConfig.xlsx**
```
actionId        actionType    targetType   apiName         parameters                    executeFrame  conditions
AddBuff_Fire    AddBuff       Self         AddBuff_API     buffId:buff_fire;trackBuff:true    0        ""
PlayFX_Fireball PlayFX        Self         PlayFX_API      fxName:fireball_fx;duration:2.0    0        ""
DealDamage_100  DealDamage    HitTarget    DealDamage_API  damage:100;damageType:Magic        0        "target.faction == Enemy"
AddBuff_Burn    AddBuff       HitTarget    AddBuff_API     buffId:buff_burn;trackBuff:false   0        ""
```

#### 12.8.2 燃烧Buff配置示例

**BuffLogicConfig.xlsx**
```
buffId      logicType     actionSequence      actionParams        tickInterval  conditions
buff_burn   OnStart       PlayFX|AttributeModify  burn_fx|health:-5   0           ""
buff_burn   OnTick        AttributeModify     health:-10          1.0           ""
buff_burn   OnRemove      PlayFX              burn_end_fx         0             ""
buff_burn   OnLayerChange AttributeModify     health:-5           0             "layerChange > 0"
```

### 12.9 使用示例

#### 12.9.1 创建配置驱动的技能

```csharp
public class SkillConfigIntegration : MonoBehaviour
{
    public static void CreateConfigDrivenSkill(Unit unit, int skillConfigId)
    {
        // 获取基础配置
        var skillConfig = ConfigMgr.SkillConfig.Get(skillConfigId);
        if (skillConfig == null) return;

        // 创建配置驱动的技能
        var skillGO = new GameObject($"ConfigSkill_{skillConfig.SkillName}");
        skillGO.transform.SetParent(unit.transform);

        var skill = skillGO.AddComponent<ConfigDrivenSkill>();

        // 应用基础配置
        skill.skillID = skillConfig.SkillId.ToString();
        skill.skillName = skillConfig.SkillName;
        skill.category = skillConfig.Category;
        skill.skillFramesDuration = skillConfig.FramesDuration;
        skill.postMoveDuration = skillConfig.PostMoveDuration;

        // 技能会在初始化时自动加载逻辑配置
        unit.AddSkill(skill);

        GameLogManager.Log($"创建配置驱动技能: {skillConfig.SkillName}", "SkillConfigIntegration");
    }
}
```

#### 12.9.2 创建配置驱动的Buff

```csharp
public class BuffConfigIntegration : MonoBehaviour
{
    public static void CreateConfigDrivenBuff(string buffId, BuffHandler target, GameObject caster)
    {
        // 获取基础配置
        var buffConfig = ConfigMgr.BuffConfig.Get(buffId);
        if (buffConfig == null) return;

        // 创建配置驱动的Buff
        var buff = ScriptableObject.CreateInstance<ConfigDrivenBuff>();

        // 应用基础配置
        buff.InitializeFromConfig(buffConfig);

        // 加载逻辑配置
        buff.InitializeFromLogicConfig(buffId);

        // 添加到目标
        target.AddBuff(buffId, caster);

        GameLogManager.Log($"创建配置驱动Buff: {buffConfig.BuffName}", "BuffConfigIntegration");
    }
}
```

### 12.10 配置验证工具

#### 12.10.1 配置完整性检查

```csharp
public static class ConfigValidationTool
{
    [MenuItem("Tools/Config/Validate Logic Configs")]
    public static void ValidateLogicConfigs()
    {
        ValidateSkillLogicConfigs();
        ValidateBuffLogicConfigs();
        ValidateAPIActionConfigs();

        Debug.Log("配置验证完成");
    }

    private static void ValidateSkillLogicConfigs()
    {
        foreach (var config in ConfigMgr.SkillLogicConfig.Data.Values)
        {
            // 检查技能是否存在
            if (ConfigMgr.SkillConfig.Get(config.SkillId) == null)
            {
                Debug.LogError($"技能逻辑配置引用了不存在的技能: {config.SkillId}");
            }

            // 检查动作序列和参数是否匹配
            if (config.ActionSequence.Length != config.ActionParams.Length)
            {
                Debug.LogError($"技能 {config.SkillId} 的动作序列和参数数量不匹配");
            }

            // 检查条件语法
            if (!string.IsNullOrEmpty(config.TriggerCondition))
            {
                try
                {
                    ConditionFactory.CreateCondition(config.TriggerCondition);
                }
                catch (Exception e)
                {
                    Debug.LogError($"技能 {config.SkillId} 的触发条件语法错误: {e.Message}");
                }
            }
        }
    }

    private static void ValidateBuffLogicConfigs()
    {
        foreach (var config in ConfigMgr.BuffLogicConfig.Data.Values)
        {
            // 检查Buff是否存在
            if (ConfigMgr.BuffConfig.Get(config.BuffId) == null)
            {
                Debug.LogError($"Buff逻辑配置引用了不存在的Buff: {config.BuffId}");
            }

            // 检查动作序列和参数是否匹配
            if (config.ActionSequence.Length != config.ActionParams.Length)
            {
                Debug.LogError($"Buff {config.BuffId} 的动作序列和参数数量不匹配");
            }
        }
    }

    private static void ValidateAPIActionConfigs()
    {
        foreach (var config in ConfigMgr.APIActionConfig.Data.Values)
        {
            // 检查API方法是否存在
            if (!IsValidAPIMethod(config.ApiName))
            {
                Debug.LogError($"API动作配置引用了不存在的API方法: {config.ApiName}");
            }

            // 检查参数格式
            if (!string.IsNullOrEmpty(config.Parameters))
            {
                try
                {
                    ParseParameters(config.Parameters);
                }
                catch (Exception e)
                {
                    Debug.LogError($"API动作 {config.ActionId} 的参数格式错误: {e.Message}");
                }
            }
        }
    }

    private static bool IsValidAPIMethod(string apiName)
    {
        // 检查API方法是否在CrossSystemAPI中存在
        var validMethods = new HashSet<string>
        {
            "AddBuff_API",
            "RemoveBuff_API",
            "AttributeModifyCapability_API",
            "PlayFX_API",
            "DealDamage_API",
            "Heal_API"
        };

        return validMethods.Contains(apiName);
    }

    private static Dictionary<string, string> ParseParameters(string paramString)
    {
        var result = new Dictionary<string, string>();
        var pairs = paramString.Split(';');

        foreach (var pair in pairs)
        {
            var keyValue = pair.Split(':');
            if (keyValue.Length != 2)
            {
                throw new ArgumentException($"参数格式错误: {pair}");
            }

            result[keyValue[0].Trim()] = keyValue[1].Trim();
        }

        return result;
    }
}
```

### 12.11 实施建议

#### 12.11.1 分阶段实施

**第一阶段：基础框架搭建**
1. 实现逻辑动作接口和基础类
2. 创建条件系统和解析器
3. 实现API动作工厂

**第二阶段：核心功能实现**
1. 实现技能逻辑配置系统
2. 实现Buff逻辑配置系统
3. 创建配置验证工具

**第三阶段：系统集成**
1. 集成到现有技能系统
2. 集成到现有Buff系统
3. 完善错误处理和日志

**第四阶段：优化和扩展**
1. 性能优化
2. 添加更多API动作类型
3. 完善配置工具

#### 12.11.2 配置管理最佳实践

**配置表设计原则**
- **模块化**：将不同类型的逻辑分别配置
- **可读性**：使用清晰的命名和注释
- **可维护性**：避免过度复杂的条件表达式
- **可扩展性**：预留扩展字段

**版本控制策略**
- **配置表版本化**：为配置表添加版本号
- **向后兼容**：保持配置格式的向后兼容性
- **变更记录**：记录配置变更历史

#### 12.11.3 性能考虑

**缓存策略**
```csharp
public class LogicConfigCache
{
    private static Dictionary<int, List<ILogicAction>> skillLogicCache =
        new Dictionary<int, List<ILogicAction>>();

    public static List<ILogicAction> GetSkillLogic(int skillId)
    {
        if (!skillLogicCache.TryGetValue(skillId, out var actions))
        {
            actions = LogicConfigParser.ParseSkillLogic(skillId);
            skillLogicCache[skillId] = actions;
        }

        return actions;
    }

    public static void ClearCache()
    {
        skillLogicCache.Clear();
    }
}
```

**条件预编译**
```csharp
public class PrecompiledCondition : ICondition
{
    private Func<object, bool> compiledCondition;

    public PrecompiledCondition(string expression)
    {
        // 将条件表达式预编译为委托
        compiledCondition = CompileCondition(expression);
    }

    public bool Evaluate(object context)
    {
        return compiledCondition(context);
    }

    private Func<object, bool> CompileCondition(string expression)
    {
        // 使用表达式树或其他技术预编译条件
        // 这里简化实现
        return context => true;
    }
}
```

### 12.12 扩展方向

#### 12.12.1 可视化配置编辑器

```csharp
public class LogicConfigEditor : EditorWindow
{
    [MenuItem("Tools/Config/Logic Config Editor")]
    public static void ShowWindow()
    {
        GetWindow<LogicConfigEditor>("逻辑配置编辑器");
    }

    private void OnGUI()
    {
        GUILayout.Label("技能逻辑配置", EditorStyles.boldLabel);

        // 技能选择
        // 逻辑类型选择
        // 条件编辑器
        // 动作序列编辑器
        // 预览和验证
    }
}
```

#### 12.12.2 热更新支持

```csharp
public class HotUpdateConfigManager : MonoBehaviour
{
    public static void ReloadLogicConfigs()
    {
        // 重新加载配置
        ConfigMgr.ReloadConfigs();

        // 清除缓存
        LogicConfigCache.ClearCache();

        // 通知所有配置驱动的对象重新加载
        var skills = FindObjectsOfType<ConfigDrivenSkill>();
        foreach (var skill in skills)
        {
            skill.ReloadLogicFromConfig();
        }

        var buffs = FindObjectsOfType<ConfigDrivenBuff>();
        foreach (var buff in buffs)
        {
            buff.ReloadLogicFromConfig();
        }

        GameLogManager.Log("逻辑配置热更新完成", "HotUpdate");
    }
}
```

#### 12.12.3 调试和分析工具

```csharp
public class LogicExecutionProfiler
{
    private static Dictionary<string, float> executionTimes = new Dictionary<string, float>();
    private static Dictionary<string, int> executionCounts = new Dictionary<string, int>();

    public static void ProfileExecution(string actionName, System.Action action)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            action();
        }
        finally
        {
            stopwatch.Stop();

            if (!executionTimes.ContainsKey(actionName))
            {
                executionTimes[actionName] = 0f;
                executionCounts[actionName] = 0;
            }

            executionTimes[actionName] += stopwatch.ElapsedMilliseconds;
            executionCounts[actionName]++;
        }
    }

    [MenuItem("Tools/Config/Show Logic Performance")]
    public static void ShowPerformanceReport()
    {
        foreach (var kvp in executionTimes)
        {
            string actionName = kvp.Key;
            float totalTime = kvp.Value;
            int count = executionCounts[actionName];
            float avgTime = totalTime / count;

            Debug.Log($"动作: {actionName}, 总时间: {totalTime}ms, 执行次数: {count}, 平均时间: {avgTime:F2}ms");
        }
    }
}
```

### 12.13 总结

通过API逻辑配置扩展方案，我们实现了：

1. **完全的配置驱动**：技能和Buff的逻辑完全由配置表驱动
2. **强大的表达能力**：支持复杂的条件判断和动作序列
3. **高度的可扩展性**：易于添加新的API动作和条件类型
4. **良好的性能**：通过缓存和预编译优化执行效率
5. **完善的工具链**：包含验证、调试和性能分析工具

这个方案使策划能够通过Excel配置实现复杂的游戏逻辑，真正实现了配置与代码的彻底分离，大大提升了开发效率和迭代速度。

#### 核心优势

- **策划友好**：策划可以直接通过Excel配置复杂逻辑
- **类型安全**：编译期检查确保配置的正确性
- **高性能**：优化的执行引擎保证运行时效率
- **易维护**：清晰的架构和完善的工具支持
- **可扩展**：模块化设计便于功能扩展

#### 应用场景

- **技能系统**：配置技能的各种效果和逻辑
- **Buff系统**：配置Buff的生命周期和效果
- **触发器系统**：配置复杂的触发条件和响应
- **AI系统**：配置AI的行为逻辑
- **任务系统**：配置任务的完成条件和奖励

通过这个扩展方案，项目将具备强大的配置驱动能力，为后续的功能开发和内容制作提供坚实的基础。